// API Configuration
// IMPORTANT: Use a single, consistent base URL across auth and API.
// Default to localhost for development to avoid cross-domain cookie issues.
export const API_CONFIG = {
  BASE_URL: (import.meta as any).env?.VITE_API_URL || 'http://localhost:3001',
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
} as const

// API Endpoints
export const API_ENDPOINTS = {
  // Health and index
  HEALTH: '/health',
  INDEX: '/',
  API_CLIENT: '/apiClient',

  // Auth endpoints (updated to match new API)
  AUTH: {
    SIGNUP: '/v1/auth/sign-up/email',
    SIGNIN: '/v1/auth/sign-in/email',
    SIGNOUT: '/v1/auth/sign-out',
    REFRESH: '/get-session',
    FORGOT_PASSWORD: '/v1/auth/forgot-password',
    RESET_PASSWORD: '/v1/auth/reset-password',
    CALLBACK: '/v1/auth/callback',
    EMAIL_VERIFY: '/v1/auth/email/verify',
  },

  // User endpoints
  USERS: {
    PROFILE: '/profile',
    VERIFY_EMAIL: '/verify-email',
  },

  // Profile endpoints (enhanced)
  PROFILES: {
    ME: '/v1/profiles/me',
  },

  // Organization endpoints
  ORGANIZATIONS: {
    CURRENT: '/v1/organizations/current',
    MEMBERS: '/v1/organizations/current/members',
    MEMBER: '/v1/organizations/current/members/:memberIdOrEmail',
    MEMBER_ROLE: '/v1/organizations/current/members/:memberId/role',
    INVITATIONS_ACCEPT: '/v1/organizations/invitations/accept',
  },

  // Workspace endpoints
  WORKSPACES: {
    CURRENT: '/v1/workspaces/current',
    INVITATIONS: '/v1/workspaces/invitations',
    INVITATION: '/v1/workspaces/invitations/:id',
    RESEND_INVITATION: '/v1/workspaces/invitations/:id/resend',
  },

  // Listing endpoints
  LISTINGS: {
    LIST: '/v1/listings',
    CREATE: '/v1/listings',
    GET: '/v1/listings/:id',
    UPDATE: '/v1/listings/:id',
    DELETE: '/v1/listings/:id',
    BULK_CSV: '/v1/listings/bulk/csv',
    SAVE_DRAFT: '/v1/listings/draft',
    UPDATE_DRAFT: '/v1/listings/:id/draft',
    STATUS_HISTORY: '/v1/listings/:id/status-history',
    NOTES: '/v1/listings/:id/notes',
    NOTE: '/v1/listings/:listingId/notes/:noteId',
  },

  // File endpoints
  FILES: {
    UPLOAD: '/v1/files/upload',
    GET: '/v1/files/:id',
    DELETE: '/v1/files/:id',
  },

  // Logs endpoints
  LOGS: {
    BY_PATH: '/v1/logs/by-path',
    DETAIL: '/v1/logs/:id',
  },
} as const

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
} as const