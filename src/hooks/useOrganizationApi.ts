import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';
import { toast } from '@/hooks/use-toast';
import type { components } from '@/types/api';

// Query Keys
export const organizationQueryKeys = {
  all: ['organization'] as const,
  current: () => [...organizationQueryKeys.all, 'current'] as const,
  members: () => [...organizationQueryKeys.all, 'members'] as const,
};

// Types
type Organization = components['schemas']['Organization'];
type UpdateOrganizationRequest = components['schemas']['UpdateOrganizationRequest'];
type MembersListResponse = components['schemas']['MembersListResponse'];
type InviteMemberRequest = components['schemas']['InviteMemberRequest'];
type UpdateMemberRoleRequest = components['schemas']['UpdateMemberRoleRequest'];
type AcceptInvitationRequest = components['schemas']['AcceptInvitationRequest'];
type SuccessResponse = components['schemas']['SuccessResponse'];

// Organization Queries
export function useCurrentOrganizationQuery(enabled = true) {
  return useQuery({
    queryKey: organizationQueryKeys.current(),
    queryFn: () => apiClient.getCurrentOrganization(),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 404 (organization not found) or 401 (unauthorized)
      if (error?.status === 404 || error?.status === 401) {
        return false;
      }
      return failureCount < 3;
    },
  });
}

export function useOrganizationMembersQuery(enabled = true) {
  return useQuery({
    queryKey: organizationQueryKeys.members(),
    queryFn: () => apiClient.getOrganizationMembers(),
    enabled,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: (failureCount, error: any) => {
      if (error?.status === 404 || error?.status === 401) {
        return false;
      }
      return failureCount < 3;
    },
  });
}

// Organization Mutations
export function useUpdateOrganizationMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateOrganizationRequest) => 
      apiClient.updateOrganization(data),
    onSuccess: (data) => {
      // Update the organization cache
      queryClient.setQueryData(organizationQueryKeys.current(), data);
      
      toast({
        title: 'Success',
        description: 'Organization updated successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error?.message || 'Failed to update organization',
        variant: 'destructive',
      });
    },
  });
}

// Member Management Mutations
export function useInviteMemberMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: InviteMemberRequest) => 
      apiClient.inviteMember(data),
    onSuccess: () => {
      // Invalidate members list to refetch
      queryClient.invalidateQueries({ queryKey: organizationQueryKeys.members() });
      
      toast({
        title: 'Success',
        description: 'Member invitation sent successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error?.message || 'Failed to invite member',
        variant: 'destructive',
      });
    },
  });
}

export function useRemoveMemberMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (memberIdOrEmail: string) => 
      apiClient.removeMember(memberIdOrEmail),
    onSuccess: () => {
      // Invalidate members list to refetch
      queryClient.invalidateQueries({ queryKey: organizationQueryKeys.members() });
      
      toast({
        title: 'Success',
        description: 'Member removed successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error?.message || 'Failed to remove member',
        variant: 'destructive',
      });
    },
  });
}

export function useUpdateMemberRoleMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ memberId, roleData }: { memberId: string; roleData: UpdateMemberRoleRequest }) => 
      apiClient.updateMemberRole(memberId, roleData),
    onSuccess: () => {
      // Invalidate members list to refetch
      queryClient.invalidateQueries({ queryKey: organizationQueryKeys.members() });
      
      toast({
        title: 'Success',
        description: 'Member role updated successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error?.message || 'Failed to update member role',
        variant: 'destructive',
      });
    },
  });
}

// Invitation Management
export function useAcceptInvitationMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: AcceptInvitationRequest) => 
      apiClient.acceptInvitation(data),
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: organizationQueryKeys.all });
      queryClient.invalidateQueries({ queryKey: ['auth', 'session'] });
      
      toast({
        title: 'Success',
        description: 'Invitation accepted successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error?.message || 'Failed to accept invitation',
        variant: 'destructive',
      });
    },
  });
}
