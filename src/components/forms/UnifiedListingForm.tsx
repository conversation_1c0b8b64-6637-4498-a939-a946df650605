import { useState, useC<PERSON>back, ComponentType } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Building2,
  DollarSign,
  FileText,
  TrendingUp,
  CheckCircle,
  ArrowLeft,
  ArrowRight,
  Save,
  Eye,
  X,
  MapPin,
  Calendar,
  Users,
  Clock,
  Target,
  Briefcase,
  Award,
  HandHeart,
  PieChart,
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import {
  useCreateListingMutation,
  useUpdateListingMutation,
  useSaveDraftListingMutation,
} from "@/hooks/useQueryApi";
import type {
  CreateListingRequest,
  UpdateListingRequest,
  SaveDraftListingRequest,
} from "@/lib/api-client";
import type { ListingFormData } from "@/types";
import { parseCurrency, formatCurrencyInput, formatCurrencySafe } from "@/lib/formatters";
import { cn } from "@/lib/utils";
import { ButtonSpinner } from "@/components/ui/loading-overlay";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Form modes
export type FormMode = 'create' | 'edit' | 'view';

// Validation types
export interface ValidationRule {
  required?: boolean;
  type?: 'text' | 'number' | 'currency' | 'email' | 'url';
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  message?: string;
}

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

// Validation schema
const validationSchema: Record<keyof ListingFormData, ValidationRule> = {
  // Required fields
  businessName: {
    required: true,
    type: 'text',
    minLength: 2,
    maxLength: 100,
    pattern: /^[a-zA-Z0-9\s&.,'\-()]+$/,
    message: 'Business name must be 2-100 characters and contain only letters, numbers, and common business symbols'
  },
  industry: {
    required: true,
    message: 'Please select an industry'
  },
  askingPrice: {
    required: true,
    type: 'currency',
    min: 1000,
    max: 100000000,
    message: 'Asking price must be between $1,000 and $100,000,000'
  },
  cashFlow: {
    required: true,
    type: 'currency',
    min: 0,
    max: 50000000,
    message: 'Cash flow must be between $0 and $50,000,000'
  },
  status: {
    required: true,
    message: 'Please select a status'
  },

  // Numeric fields
  yearEstablished: {
    type: 'number',
    min: 1800,
    max: new Date().getFullYear(),
    message: `Year established must be between 1800 and ${new Date().getFullYear()}`
  },
  employees: {
    type: 'number',
    min: 0,
    max: 10000,
    message: 'Number of employees must be between 0 and 10,000'
  },
  ownerHours: {
    type: 'number',
    min: 0,
    max: 168,
    message: 'Owner hours per week must be between 0 and 168'
  },

  // Currency fields
  annualRevenue: {
    type: 'currency',
    min: 0,
    max: 900000000000,
    message: 'Annual revenue must be between $0 and $900,000,000,000'
  },

  // Text fields with limits
  location: {
    type: 'text',
    maxLength: 100,
    message: 'Location must be less than 100 characters'
  },
  businessDescription: {
    type: 'text',
    maxLength: 5000,
    message: 'Business description must be less than 5,000 characters'
  },
  briefDescription: {
    type: 'text',
    maxLength: 500,
    message: 'Brief description must be less than 500 characters'
  },
  growthOpportunities: {
    type: 'text',
    maxLength: 2000,
    message: 'Growth opportunities must be less than 2,000 characters'
  },
  reasonForSale: {
    type: 'text',
    maxLength: 2000,
    message: 'Reason for sale must be less than 2,000 characters'
  },
  trainingPeriod: {
    type: 'text',
    maxLength: 200,
    message: 'Training period must be less than 200 characters'
  },
  supportType: {
    type: 'text',
    maxLength: 200,
    message: 'Support type must be less than 200 characters'
  },
  equipmentHighlights: {
    type: 'text',
    maxLength: 2000,
    message: 'Equipment highlights must be less than 2,000 characters'
  },
  supplierRelationships: {
    type: 'text',
    maxLength: 2000,
    message: 'Supplier relationships must be less than 2,000 characters'
  },
  realEstateStatus: {
    type: 'text',
    message: 'Please select a real estate status'
  },
  financingAvailable: {
    message: 'Please specify if financing is available'
  }
};

// Validation utility functions
const validateField = (field: keyof ListingFormData, value: string | boolean): ValidationResult => {
  const rule = validationSchema[field];
  if (!rule) return { isValid: true };

  const stringValue = typeof value === 'boolean' ? value.toString() : value.trim();

  // Required field validation
  if (rule.required && (!stringValue || stringValue === '')) {
    return { isValid: false, error: `${field} is required` };
  }

  // Skip further validation if field is empty and not required
  if (!stringValue && !rule.required) {
    return { isValid: true };
  }

  // Type-specific validation
  switch (rule.type) {
    case 'currency':
      const currencyValue = parseCurrency(stringValue);
      if (isNaN(currencyValue)) {
        return { isValid: false, error: 'Please enter a valid currency amount' };
      }
      if (rule.min !== undefined && currencyValue < rule.min) {
        return { isValid: false, error: rule.message || `Minimum value is ${formatCurrencySafe(rule.min.toString())}` };
      }
      if (rule.max !== undefined && currencyValue > rule.max) {
        return { isValid: false, error: rule.message || `Maximum value is ${formatCurrencySafe(rule.max.toString())}` };
      }
      break;

    case 'number':
      const numValue = parseInt(stringValue);
      if (isNaN(numValue)) {
        return { isValid: false, error: 'Please enter a valid number' };
      }
      if (rule.min !== undefined && numValue < rule.min) {
        return { isValid: false, error: rule.message || `Minimum value is ${rule.min}` };
      }
      if (rule.max !== undefined && numValue > rule.max) {
        return { isValid: false, error: rule.message || `Maximum value is ${rule.max}` };
      }
      break;

    case 'email':
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailPattern.test(stringValue)) {
        return { isValid: false, error: 'Please enter a valid email address' };
      }
      break;

    case 'url':
      try {
        new URL(stringValue);
      } catch {
        return { isValid: false, error: 'Please enter a valid URL' };
      }
      break;

    case 'text':
    default:
      // Pattern validation
      if (rule.pattern && !rule.pattern.test(stringValue)) {
        return { isValid: false, error: rule.message || 'Invalid format' };
      }
      break;
  }

  // Length validation
  if (rule.minLength !== undefined && stringValue.length < rule.minLength) {
    return { isValid: false, error: rule.message || `Minimum length is ${rule.minLength} characters` };
  }
  if (rule.maxLength !== undefined && stringValue.length > rule.maxLength) {
    return { isValid: false, error: rule.message || `Maximum length is ${rule.maxLength} characters` };
  }

  return { isValid: true };
};

// Integrated Step Progress Component
interface StepProgressProps {
  steps: StepConfig[];
  currentStep: FormStep;
  completedSteps: Set<FormStep>;
  onStepClick?: (step: FormStep) => void;
  mode: FormMode;
}

function StepProgress({ steps, currentStep, completedSteps, onStepClick, mode }: StepProgressProps) {
  const isViewMode = mode === 'view';
  const currentStepIndex = steps.findIndex(step => step.id === currentStep);

  return (
    <TooltipProvider>
      <div className="w-full px-4 py-6">
        <div className="flex items-center justify-between relative">
          {/* Progress line background */}
          <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-muted -translate-y-1/2 z-0" />

          {/* Progress line foreground */}
          <div
            className="absolute top-1/2 left-0 h-0.5 bg-primary -translate-y-1/2 z-0 transition-all duration-300"
            style={{
              width: `${(currentStepIndex / (steps.length - 1)) * 100}%`
            }}
          />

          {/* Step icons */}
          {steps.map((step, index) => {
            const isCompleted = completedSteps.has(step.id);
            const isCurrent = step.id === currentStep;
            const isPending = !isCompleted && !isCurrent;
            const isClickable = !isViewMode && onStepClick && (isCompleted || index <= currentStepIndex);

            const Icon = step.icon;

            return (
              <Tooltip key={step.id}>
                <TooltipTrigger asChild>
                  <button
                    onClick={() => isClickable ? onStepClick(step.id) : undefined}
                    disabled={!isClickable}
                    className={cn(
                      "relative z-10 flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
                      {
                        // Completed state
                        "bg-primary border-primary text-primary-foreground hover:bg-primary/90": isCompleted,
                        // Current state
                        "bg-primary border-primary text-primary-foreground ring-2 ring-primary ring-offset-2": isCurrent,
                        // Pending state
                        "bg-background border-muted-foreground/30 text-muted-foreground": isPending,
                        // Clickable states
                        "cursor-pointer hover:border-primary/60 hover:text-primary": isClickable && !isCurrent,
                        "cursor-not-allowed": !isClickable,
                      }
                    )}
                    aria-label={`Step ${index + 1}: ${step.title}`}
                    aria-current={isCurrent ? "step" : undefined}
                  >
                    {isCompleted ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      <Icon className="w-5 h-5" />
                    )}
                  </button>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="max-w-xs">
                  <div className="space-y-1">
                    <p className="font-medium">{step.title}</p>
                    <p className="text-xs text-muted-foreground">{step.description}</p>
                    {isCompleted && (
                      <p className="text-xs text-green-600 font-medium">✓ Completed</p>
                    )}
                    {isCurrent && (
                      <p className="text-xs text-blue-600 font-medium">→ Current Step</p>
                    )}
                  </div>
                </TooltipContent>
              </Tooltip>
            );
          })}
        </div>

        {/* Mobile step indicator */}
        <div className="mt-4 text-center text-sm text-muted-foreground md:hidden">
          Step {currentStepIndex + 1} of {steps.length}
        </div>
      </div>
    </TooltipProvider>
  );
}

// Reusable field components
interface FieldProps {
  label: string;
  value: string | boolean;
  onChange: (value: string | boolean) => void;
  onBlur?: () => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  icon?: ComponentType<{ className?: string }>;
  mode: FormMode;
  maxLength?: number;
  showCharCount?: boolean;
}

// Text input field
function TextField({ label, value, onChange, onBlur, placeholder, required, disabled, error, icon: Icon, mode, maxLength, showCharCount }: FieldProps) {
  const isViewMode = mode === 'view';
  const stringValue = value as string;

  if (isViewMode) {
    return (
      <div className="space-y-2">
        <Label className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
          {Icon && <Icon className="h-4 w-4" />}
          {label}
        </Label>
        <div className="text-base font-medium">
          {value || "Not provided"}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <Label className="flex items-center gap-2 text-sm font-medium">
        {Icon && <Icon className="h-4 w-4" />}
        {label}
        {required && <span className="text-red-500">*</span>}
      </Label>
      <Input
        value={stringValue}
        onChange={(e) => onChange(e.target.value)}
        onBlur={onBlur}
        placeholder={placeholder}
        disabled={disabled}
        maxLength={maxLength}
        className={cn(
          "h-12 text-base transition-all duration-200",
          error && "border-red-500 focus:border-red-500",
          !error && stringValue && "border-green-500 focus:border-green-500"
        )}
      />
      <div className="flex justify-between items-center">
        {error && (
          <p className="text-xs text-red-500 flex items-center gap-1">
            <span className="w-1 h-1 bg-red-500 rounded-full"></span>
            {error}
          </p>
        )}
        {!error && showCharCount && maxLength && (
          <p className="text-xs text-muted-foreground ml-auto">
            {stringValue.length}/{maxLength}
          </p>
        )}
      </div>
    </div>
  );
}

// Number input field
interface NumberFieldProps extends Omit<FieldProps, 'value' | 'onChange'> {
  value: string;
  onChange: (value: string) => void;
  min?: number;
  max?: number;
  step?: number;
}

function NumberField({ label, value, onChange, onBlur, placeholder, required, disabled, error, icon: Icon, mode, min, max, step = 1 }: NumberFieldProps) {
  const isViewMode = mode === 'view';

  if (isViewMode) {
    return (
      <div className="space-y-2">
        <Label className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
          {Icon && <Icon className="h-4 w-4" />}
          {label}
        </Label>
        <div className="text-base font-medium">
          {value || "Not provided"}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <Label className="flex items-center gap-2 text-sm font-medium">
        {Icon && <Icon className="h-4 w-4" />}
        {label}
        {required && <span className="text-red-500">*</span>}
      </Label>
      <Input
        type="number"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onBlur={onBlur}
        placeholder={placeholder}
        disabled={disabled}
        min={min}
        max={max}
        step={step}
        className={cn(
          "h-12 text-base transition-all duration-200",
          error && "border-red-500 focus:border-red-500",
          !error && value && "border-green-500 focus:border-green-500"
        )}
      />
      {error && (
        <p className="text-xs text-red-500 flex items-center gap-1">
          <span className="w-1 h-1 bg-red-500 rounded-full"></span>
          {error}
        </p>
      )}
    </div>
  );
}

// Currency input field
function CurrencyField({ label, value, onChange, onBlur, placeholder = "$0", required, disabled, error, icon: Icon, mode }: FieldProps) {
  const isViewMode = mode === 'view';
  const stringValue = value as string;

  if (isViewMode) {
    return (
      <div className="space-y-2">
        <Label className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
          {Icon && <Icon className="h-4 w-4" />}
          {label}
        </Label>
        <div className="text-base font-medium">
          {value ? formatCurrencySafe(stringValue) : "Not provided"}
        </div>
      </div>
    );
  }

  const handleCurrencyChange = (inputValue: string) => {
    const formatted = formatCurrencyInput(inputValue);
    onChange(formatted);
  };

  return (
    <div className="space-y-2">
      <Label className="flex items-center gap-2 text-sm font-medium">
        {Icon && <Icon className="h-4 w-4" />}
        {label}
        {required && <span className="text-red-500">*</span>}
      </Label>
      <Input
        type="text"
        value={stringValue}
        onChange={(e) => handleCurrencyChange(e.target.value)}
        onBlur={onBlur}
        placeholder={placeholder}
        disabled={disabled}
        className={cn(
          "h-12 text-base transition-all duration-200",
          error && "border-red-500 focus:border-red-500",
          !error && stringValue && "border-green-500 focus:border-green-500"
        )}
      />
      {error && (
        <p className="text-xs text-red-500 flex items-center gap-1">
          <span className="w-1 h-1 bg-red-500 rounded-full"></span>
          {error}
        </p>
      )}
    </div>
  );
}

// Select field
interface SelectFieldProps extends Omit<FieldProps, 'onChange'> {
  options: { value: string; label: string }[];
  onChange: (value: string) => void;
}

function SelectField({ label, value, onChange, options, required, disabled, error, icon: Icon, mode }: SelectFieldProps) {
  const isViewMode = mode === 'view';

  if (isViewMode) {
    const selectedOption = options.find(opt => opt.value === value);
    return (
      <div className="space-y-2">
        <Label className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
          {Icon && <Icon className="h-4 w-4" />}
          {label}
        </Label>
        <div className="text-base font-medium">
          {selectedOption?.label || "Not provided"}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <Label className="flex items-center gap-2 text-sm font-medium">
        {Icon && <Icon className="h-4 w-4" />}
        {label}
        {required && <span className="text-red-500">*</span>}
      </Label>
      <Select value={value as string} onValueChange={onChange} disabled={disabled}>
        <SelectTrigger className={cn(
          "h-12 text-base",
          error && "border-red-500 focus:border-red-500"
        )}>
          <SelectValue placeholder="Select an option" />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {error && (
        <p className="text-xs text-red-500 flex items-center gap-1">
          <span className="w-1 h-1 bg-red-500 rounded-full"></span>
          {error}
        </p>
      )}
    </div>
  );
}

// Textarea field
interface TextareaFieldProps extends FieldProps {
  rows?: number;
}

function TextareaField({ label, value, onChange, onBlur, placeholder, required, disabled, error, icon: Icon, mode, maxLength, showCharCount, rows = 4 }: TextareaFieldProps) {
  const isViewMode = mode === 'view';
  const stringValue = value as string;

  if (isViewMode) {
    return (
      <div className="space-y-2">
        <Label className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
          {Icon && <Icon className="h-4 w-4" />}
          {label}
        </Label>
        <div className="text-base whitespace-pre-wrap">
          {value || "Not provided"}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <Label className="flex items-center gap-2 text-sm font-medium">
        {Icon && <Icon className="h-4 w-4" />}
        {label}
        {required && <span className="text-red-500">*</span>}
      </Label>
      <Textarea
        value={stringValue}
        onChange={(e) => onChange(e.target.value)}
        onBlur={onBlur}
        placeholder={placeholder}
        disabled={disabled}
        rows={rows}
        maxLength={maxLength}
        className={cn(
          "text-base resize-none transition-all duration-200",
          error && "border-red-500 focus:border-red-500",
          !error && stringValue && "border-green-500 focus:border-green-500"
        )}
      />
      <div className="flex justify-between items-center">
        {error && (
          <p className="text-xs text-red-500 flex items-center gap-1">
            <span className="w-1 h-1 bg-red-500 rounded-full"></span>
            {error}
          </p>
        )}
        {!error && showCharCount && maxLength && (
          <p className="text-xs text-muted-foreground ml-auto">
            {stringValue.length}/{maxLength}
          </p>
        )}
      </div>
    </div>
  );
}

// Checkbox field
interface CheckboxFieldProps extends Omit<FieldProps, 'value' | 'onChange'> {
  checked: boolean;
  onChange: (checked: boolean) => void;
}

function CheckboxField({ label, checked, onChange, disabled, error, icon: Icon, mode }: CheckboxFieldProps) {
  const isViewMode = mode === 'view';

  if (isViewMode) {
    return (
      <div className="space-y-2">
        <Label className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
          {Icon && <Icon className="h-4 w-4" />}
          {label}
        </Label>
        <div className="text-base font-medium">
          {checked ? "Yes" : "No"}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center space-x-3">
        <Checkbox
          checked={checked}
          onCheckedChange={onChange}
          disabled={disabled}
          className="h-5 w-5"
        />
        <Label className="flex items-center gap-2 text-sm font-medium cursor-pointer">
          {Icon && <Icon className="h-4 w-4" />}
          {label}
        </Label>
      </div>
      {error && (
        <p className="text-xs text-red-500 flex items-center gap-1">
          <span className="w-1 h-1 bg-red-500 rounded-full"></span>
          {error}
        </p>
      )}
    </div>
  );
}

// Form step types
export type FormStep = "basic" | "financial" | "description" | "operations" | "additional" | "review";

// Form options
const industries = [
  { value: "Restaurant", label: "Restaurant" },
  { value: "Manufacturing", label: "Manufacturing" },
  { value: "Retail", label: "Retail" },
  { value: "Service", label: "Service" },
  { value: "Healthcare", label: "Healthcare" },
  { value: "Auto", label: "Auto" },
  { value: "Technology", label: "Technology" },
  { value: "Other", label: "Other" },
];

const statuses = [
  { value: "draft", label: "Draft" },
  { value: "active", label: "Active" },
  { value: "confidential", label: "Confidential" },
  { value: "under_contract", label: "Under Contract" },
  { value: "sold", label: "Sold" },
  { value: "expired", label: "Expired" },
  { value: "withdrawn", label: "Withdrawn" },
];

const realEstateStatuses = [
  { value: "owned", label: "Owned" },
  { value: "leased", label: "Leased" },
  { value: "included", label: "Included" },
  { value: "not_included", label: "Not Included" },
  { value: "negotiable", label: "Negotiable" },
];

// Props interface with discriminated union for type safety
export interface UnifiedListingFormProps {
  mode: FormMode;
  initialData?: Partial<ListingFormData>;
  listingId?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
  className?: string;
}

// Step configuration
interface StepConfig {
  id: FormStep;
  title: string;
  description: string;
  icon: ComponentType<{ className?: string }>;
  fields: (keyof ListingFormData)[];
}

const steps: StepConfig[] = [
  {
    id: "basic",
    title: "Basic Information",
    description: "Essential business details",
    icon: Building2,
    fields: ["businessName", "industry", "askingPrice", "cashFlow", "status", "location"],
  },
  {
    id: "financial",
    title: "Financial Details",
    description: "Revenue and financial information",
    icon: DollarSign,
    fields: ["annualRevenue", "realEstateStatus"],
  },
  {
    id: "description",
    title: "Business Description",
    description: "Business overview and details",
    icon: FileText,
    fields: ["businessDescription", "briefDescription"],
  },
  {
    id: "operations",
    title: "Operations & Growth",
    description: "Operational details and growth information",
    icon: TrendingUp,
    fields: ["yearEstablished", "employees", "ownerHours", "growthOpportunities", "reasonForSale"],
  },
  {
    id: "additional",
    title: "Additional Details",
    description: "Training, support, and other details",
    icon: CheckCircle,
    fields: ["trainingPeriod", "supportType", "financingAvailable", "equipmentHighlights", "supplierRelationships"],
  },
  {
    id: "review",
    title: "Review & Submit",
    description: "Review all information before submitting",
    icon: Eye,
    fields: [],
  },
];

export default function UnifiedListingForm({
  mode,
  initialData = {},
  listingId,
  onSuccess,
  onCancel,
  className,
}: UnifiedListingFormProps) {
  // Form state
  const [currentStep, setCurrentStep] = useState<FormStep>("basic");
  const [completedSteps, setCompletedSteps] = useState<Set<FormStep>>(new Set());
  const [formData, setFormData] = useState<ListingFormData>({
    // Basic Information
    businessName: initialData.businessName || "",
    industry: initialData.industry || "",
    askingPrice: initialData.askingPrice || "",
    cashFlow: initialData.cashFlow || "",
    status: initialData.status || "active",
    annualRevenue: initialData.annualRevenue || "",
    location: initialData.location || "",
    yearEstablished: initialData.yearEstablished || "",
    employees: initialData.employees || "",
    ownerHours: initialData.ownerHours || "",

    // Business Overview
    businessDescription: initialData.businessDescription || "",
    briefDescription: initialData.briefDescription || "",

    // Financial Details
    realEstateStatus: initialData.realEstateStatus || "",

    // Growth & Sale Information
    growthOpportunities: initialData.growthOpportunities || "",
    reasonForSale: initialData.reasonForSale || "",
    trainingPeriod: initialData.trainingPeriod || "",
    supportType: initialData.supportType || "",
    financingAvailable: initialData.financingAvailable || false,

    // Additional Details
    equipmentHighlights: initialData.equipmentHighlights || "",
    supplierRelationships: initialData.supplierRelationships || "",
  });

  // Validation and submission state
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // API mutations
  const createMutation = useCreateListingMutation({
    onSuccess: () => {
      toast({ title: "Success", description: "Listing created successfully!" });
      onSuccess?.();
    },
    onError: () => {
      toast({ title: "Error", description: "Failed to create listing. Please try again.", variant: "destructive" });
    },
  });

  const updateMutation = useUpdateListingMutation({
    onSuccess: () => {
      toast({ title: "Success", description: "Listing updated successfully!" });
      onSuccess?.();
    },
    onError: () => {
      toast({ title: "Error", description: "Failed to update listing. Please try again.", variant: "destructive" });
    },
  });

  const saveDraftMutation = useSaveDraftListingMutation({
    onSuccess: () => {
      toast({ title: "Draft Saved", description: "Your changes have been saved as a draft." });
    },
  });



  // Helper functions
  const isViewMode = mode === 'view';
  const isEditMode = mode === 'edit';
  const isCreateMode = mode === 'create';

  // Get current step index
  const currentStepIndex = steps.findIndex(step => step.id === currentStep);
  const totalSteps = isViewMode ? steps.length - 1 : steps.length; // Exclude review step in view mode

  // Step validation
  const validateCurrentStep = useCallback(() => {
    const stepFields = steps.find(s => s.id === currentStep)?.fields || [];
    const stepErrors: Record<string, string> = {};

    stepFields.forEach(field => {
      const result = validateField(field, formData[field]);
      if (!result.isValid && result.error) {
        stepErrors[field] = result.error;
      }
    });

    setValidationErrors(prev => ({
      ...prev,
      ...stepErrors,
    }));

    return Object.keys(stepErrors).length === 0;
  }, [currentStep, formData]);

  // Navigation helpers
  const canGoNext = currentStepIndex < totalSteps - 1;
  const canGoPrevious = currentStepIndex > 0;

  const handleNext = useCallback(() => {
    if (!canGoNext) return;

    // Validate current step before proceeding
    const isValid = validateCurrentStep();
    if (!isValid) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the current step before proceeding.",
        variant: "destructive",
      });
      return;
    }

    // Mark current step as completed
    setCompletedSteps(prev => new Set([...prev, currentStep]));

    const nextStepIndex = currentStepIndex + 1;
    setCurrentStep(steps[nextStepIndex].id);
  }, [canGoNext, currentStepIndex, validateCurrentStep, currentStep]);

  const handlePrevious = useCallback(() => {
    if (canGoPrevious) {
      const prevStepIndex = currentStepIndex - 1;
      setCurrentStep(steps[prevStepIndex].id);
    }
  }, [canGoPrevious, currentStepIndex]);

  // Handle step click navigation
  const handleStepClick = useCallback((stepId: FormStep) => {
    if (isViewMode) return;

    const targetStepIndex = steps.findIndex(step => step.id === stepId);
    const isStepAccessible = completedSteps.has(stepId) || targetStepIndex <= currentStepIndex;

    if (isStepAccessible) {
      setCurrentStep(stepId);
    }
  }, [isViewMode, completedSteps, currentStepIndex]);

  // Form field update handler with validation
  const handleFieldChange = useCallback((field: keyof ListingFormData, value: string | boolean) => {
    if (isViewMode) return; // Prevent changes in view mode

    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear validation error for this field when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [isViewMode, validationErrors]);

  // Field validation handler (called on blur)
  const handleFieldBlur = useCallback((field: keyof ListingFormData) => {
    if (isViewMode) return;

    const result = validateField(field, formData[field]);
    if (!result.isValid && result.error) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: result.error!,
      }));
    }
  }, [isViewMode, formData]);



  // Form submission logic
  const buildSubmissionData = useCallback(() => {
    const baseData = {
      businessName: formData.businessName.trim(),
      industry: formData.industry,
      askingPrice: parseCurrency(formData.askingPrice),
      cashFlowSde: parseCurrency(formData.cashFlow),
      annualRevenue: parseCurrency(formData.annualRevenue),
      status: formData.status,
      generalLocation: formData.location.trim(),
      yearEstablished: formData.yearEstablished ? parseInt(formData.yearEstablished) : undefined,
      employees: formData.employees ? parseInt(formData.employees) : undefined,
      ownerHoursWeek: formData.ownerHours ? parseInt(formData.ownerHours) : undefined,
      listingType: "business_sale",
      teamVisibility: "all",
    };

    const details: any = {};
    let hasDetails = false;

    if (formData.businessDescription?.trim()) {
      details.businessDescription = formData.businessDescription.trim();
      hasDetails = true;
    }

    if (formData.briefDescription?.trim()) {
      details.briefDescription = formData.briefDescription.trim();
      hasDetails = true;
    }

    if (formData.growthOpportunities?.trim()) {
      details.growthOpportunities = formData.growthOpportunities
        .split("\n")
        .filter((line) => line.trim() !== "");
      hasDetails = true;
    }

    if (formData.reasonForSale?.trim()) {
      details.reasonForSale = formData.reasonForSale.trim();
      hasDetails = true;
    }

    if (formData.realEstateStatus?.trim()) {
      details.realEstateStatus = formData.realEstateStatus.trim();
      hasDetails = true;
    }

    if (formData.trainingPeriod?.trim()) {
      details.trainingPeriod = formData.trainingPeriod.trim();
      hasDetails = true;
    }

    if (formData.supportType?.trim()) {
      details.supportType = formData.supportType.trim();
      hasDetails = true;
    }

    if (formData.equipmentHighlights?.trim()) {
      details.equipmentHighlights = formData.equipmentHighlights
        .split("\n")
        .filter((line) => line.trim() !== "");
      hasDetails = true;
    }

    if (formData.supplierRelationships?.trim()) {
      details.supplierRelationships = formData.supplierRelationships.trim();
      hasDetails = true;
    }

    details.financingAvailable = formData.financingAvailable;
    hasDetails = true;

    return {
      ...baseData,
      ...(hasDetails && { details }),
    };
  }, [formData]);

  // Validate all form fields
  const validateAllFormFields = useCallback(() => {
    const allErrors: Record<string, string> = {};

    Object.keys(validationSchema).forEach(field => {
      const result = validateField(field as keyof ListingFormData, formData[field as keyof ListingFormData]);
      if (!result.isValid && result.error) {
        allErrors[field] = result.error;
      }
    });

    setValidationErrors(allErrors);
    return Object.keys(allErrors).length === 0;
  }, [formData]);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    if (isViewMode) return;

    // Validate all fields before submission
    const isValid = validateAllFormFields();
    if (!isValid) {
      toast({
        title: "Validation Error",
        description: "Please fix all validation errors before submitting the form.",
        variant: "destructive",
      });

      // Navigate to the first step with errors
      const stepsWithErrors = steps.filter(step =>
        step.fields.some(field => validationErrors[field])
      );
      if (stepsWithErrors.length > 0) {
        setCurrentStep(stepsWithErrors[0].id);
      }
      return;
    }

    setIsSubmitting(true);
    try {
      const submissionData = buildSubmissionData();

      if (isCreateMode) {
        await createMutation.mutateAsync(submissionData as CreateListingRequest);
      } else if (isEditMode && listingId) {
        await updateMutation.mutateAsync({
          listingId,
          listingData: submissionData as UpdateListingRequest,
        });
      }
    } catch (error) {
      console.error("Submission error:", error);
    } finally {
      setIsSubmitting(false);
    }
  }, [isViewMode, isCreateMode, isEditMode, listingId, buildSubmissionData, createMutation, updateMutation, validateAllFormFields, validationErrors]);

  // Handle draft saving (for create mode)
  const handleSaveDraft = useCallback(async () => {
    if (isViewMode || !isCreateMode) return;

    setIsSubmitting(true);
    try {
      const draftData = buildSubmissionData();
      await saveDraftMutation.mutateAsync({
        ...draftData,
        status: "draft",
      } as SaveDraftListingRequest);
    } catch (error) {
      console.error("Draft save error:", error);
    } finally {
      setIsSubmitting(false);
    }
  }, [isViewMode, isCreateMode, buildSubmissionData, saveDraftMutation]);

  // Handle edit mode save (validates current step only)
  const handleEditSave = useCallback(async () => {
    if (isViewMode || !isEditMode || !listingId) return;

    // Validate only the current step
    const isCurrentStepValid = validateCurrentStep();
    if (!isCurrentStepValid) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the current step before saving.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const submissionData = buildSubmissionData();
      await updateMutation.mutateAsync({
        listingId,
        listingData: submissionData as UpdateListingRequest,
      });

      toast({
        title: "Success",
        description: "Listing saved successfully!",
      });
    } catch (error) {
      console.error("Edit save error:", error);
      toast({
        title: "Error",
        description: "Failed to save listing. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [isViewMode, isEditMode, listingId, validateCurrentStep, buildSubmissionData, updateMutation]);

  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case "basic":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <TextField
              label="Business Name"
              value={formData.businessName}
              onChange={(value) => handleFieldChange("businessName", value)}
              onBlur={() => handleFieldBlur("businessName")}
              placeholder="Enter the business name"
              required
              icon={Building2}
              mode={mode}
              error={validationErrors.businessName}
              maxLength={100}
              showCharCount={true}
            />

            <SelectField
              label="Industry"
              value={formData.industry}
              onChange={(value) => handleFieldChange("industry", value)}
              options={industries}
              required
              icon={Briefcase}
              mode={mode}
              error={validationErrors.industry}
            />

            <CurrencyField
              label="Asking Price"
              value={formData.askingPrice}
              onChange={(value) => handleFieldChange("askingPrice", value)}
              onBlur={() => handleFieldBlur("askingPrice")}
              placeholder="$0"
              required
              icon={DollarSign}
              mode={mode}
              error={validationErrors.askingPrice}
            />

            <CurrencyField
              label="Cash Flow/SDE"
              value={formData.cashFlow}
              onChange={(value) => handleFieldChange("cashFlow", value)}
              onBlur={() => handleFieldBlur("cashFlow")}
              placeholder="$0"
              required
              icon={TrendingUp}
              mode={mode}
              error={validationErrors.cashFlow}
            />

            <TextField
              label="General Location"
              value={formData.location}
              onChange={(value) => handleFieldChange("location", value)}
              onBlur={() => handleFieldBlur("location")}
              placeholder="North Tampa, FL"
              icon={MapPin}
              mode={mode}
              error={validationErrors.location}
              maxLength={100}
            />

            <SelectField
              label="Status"
              value={formData.status}
              onChange={(value) => handleFieldChange("status", value)}
              options={statuses}
              required
              icon={Target}
              mode={mode}
              error={validationErrors.status}
            />
          </div>
        );

      case "financial":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <CurrencyField
              label="Annual Revenue"
              value={formData.annualRevenue}
              onChange={(value) => handleFieldChange("annualRevenue", value)}
              onBlur={() => handleFieldBlur("annualRevenue")}
              placeholder="$0"
              icon={PieChart}
              mode={mode}
              error={validationErrors.annualRevenue}
            />

            <SelectField
              label="Real Estate Status"
              value={formData.realEstateStatus}
              onChange={(value) => handleFieldChange("realEstateStatus", value)}
              options={realEstateStatuses}
              icon={Building2}
              mode={mode}
              error={validationErrors.realEstateStatus}
            />
          </div>
        );

      case "description":
        return (
          <div className="space-y-6">
            <TextareaField
              label="Business Description"
              value={formData.businessDescription}
              onChange={(value) => handleFieldChange("businessDescription", value)}
              onBlur={() => handleFieldBlur("businessDescription")}
              placeholder="Provide a comprehensive description of the business, its operations, and what makes it unique..."
              icon={FileText}
              mode={mode}
              error={validationErrors.businessDescription}
              maxLength={5000}
              showCharCount={true}
              rows={6}
            />

            <TextareaField
              label="Brief Description for Portfolio"
              value={formData.briefDescription}
              onChange={(value) => handleFieldChange("briefDescription", value)}
              onBlur={() => handleFieldBlur("briefDescription")}
              placeholder="A concise summary for portfolio listings..."
              icon={FileText}
              mode={mode}
              error={validationErrors.briefDescription}
              maxLength={500}
              showCharCount={true}
              rows={3}
            />
          </div>
        );

      case "operations":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <NumberField
              label="Year Established"
              value={formData.yearEstablished}
              onChange={(value) => handleFieldChange("yearEstablished", value)}
              onBlur={() => handleFieldBlur("yearEstablished")}
              placeholder="2020"
              icon={Calendar}
              mode={mode}
              error={validationErrors.yearEstablished}
              min={1800}
              max={new Date().getFullYear()}
            />

            <NumberField
              label="Number of Employees"
              value={formData.employees}
              onChange={(value) => handleFieldChange("employees", value)}
              onBlur={() => handleFieldBlur("employees")}
              placeholder="5"
              icon={Users}
              mode={mode}
              error={validationErrors.employees}
              min={0}
              max={10000}
            />

            <NumberField
              label="Owner Hours per Week"
              value={formData.ownerHours}
              onChange={(value) => handleFieldChange("ownerHours", value)}
              onBlur={() => handleFieldBlur("ownerHours")}
              placeholder="40"
              icon={Clock}
              mode={mode}
              error={validationErrors.ownerHours}
              min={0}
              max={168}
            />

            <div className="md:col-span-2">
              <TextareaField
                label="Growth Opportunities"
                value={formData.growthOpportunities}
                onChange={(value) => handleFieldChange("growthOpportunities", value)}
                onBlur={() => handleFieldBlur("growthOpportunities")}
                placeholder="Describe expansion possibilities, market potential, and untapped revenue streams..."
                icon={TrendingUp}
                mode={mode}
                error={validationErrors.growthOpportunities}
                maxLength={2000}
                showCharCount={true}
                rows={4}
              />
            </div>

            <div className="md:col-span-2">
              <TextareaField
                label="Reason for Sale"
                value={formData.reasonForSale}
                onChange={(value) => handleFieldChange("reasonForSale", value)}
                onBlur={() => handleFieldBlur("reasonForSale")}
                placeholder="Share the seller's motivation and transition timeline..."
                icon={HandHeart}
                mode={mode}
                error={validationErrors.reasonForSale}
                maxLength={2000}
                showCharCount={true}
                rows={4}
              />
            </div>
          </div>
        );

      case "additional":
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <TextField
              label="Training Period"
              value={formData.trainingPeriod}
              onChange={(value) => handleFieldChange("trainingPeriod", value)}
              onBlur={() => handleFieldBlur("trainingPeriod")}
              placeholder="30-60 days"
              icon={Award}
              mode={mode}
              error={validationErrors.trainingPeriod}
              maxLength={200}
            />

            <TextField
              label="Support Type"
              value={formData.supportType}
              onChange={(value) => handleFieldChange("supportType", value)}
              onBlur={() => handleFieldBlur("supportType")}
              placeholder="Full operational support"
              icon={HandHeart}
              mode={mode}
              error={validationErrors.supportType}
              maxLength={200}
            />

            <div className="md:col-span-2">
              <CheckboxField
                label="Financing Available"
                checked={formData.financingAvailable}
                onChange={(checked) => handleFieldChange("financingAvailable", checked)}
                icon={DollarSign}
                mode={mode}
                error={validationErrors.financingAvailable}
              />
            </div>

            <div className="md:col-span-2">
              <TextareaField
                label="Equipment Highlights"
                value={formData.equipmentHighlights}
                onChange={(value) => handleFieldChange("equipmentHighlights", value)}
                onBlur={() => handleFieldBlur("equipmentHighlights")}
                placeholder="List key equipment and assets included with the business..."
                icon={Award}
                mode={mode}
                error={validationErrors.equipmentHighlights}
                maxLength={2000}
                showCharCount={true}
                rows={4}
              />
            </div>

            <div className="md:col-span-2">
              <TextareaField
                label="Supplier Relationships"
                value={formData.supplierRelationships}
                onChange={(value) => handleFieldChange("supplierRelationships", value)}
                onBlur={() => handleFieldBlur("supplierRelationships")}
                placeholder="Describe key supplier relationships and contracts..."
                icon={HandHeart}
                mode={mode}
                error={validationErrors.supplierRelationships}
                maxLength={2000}
                showCharCount={true}
                rows={4}
              />
            </div>
          </div>
        );

      case "review":
        return (
          <div className="space-y-8">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">Review Your Listing</h3>
              <p className="text-muted-foreground">
                Please review all the information below before submitting your listing.
              </p>
            </div>

            {/* Review content will be implemented in the next step */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Building2 className="h-4 w-4" />
                    Basic Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Business Name:</span>
                    <span className="text-sm font-medium">{formData.businessName || "Not provided"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Industry:</span>
                    <span className="text-sm font-medium">{formData.industry || "Not provided"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Asking Price:</span>
                    <span className="text-sm font-medium">{formData.askingPrice || "Not provided"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Cash Flow:</span>
                    <span className="text-sm font-medium">{formData.cashFlow || "Not provided"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Status:</span>
                    <Badge variant="outline">{formData.status}</Badge>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Financial Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Annual Revenue:</span>
                    <span className="text-sm font-medium">{formData.annualRevenue || "Not provided"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Real Estate Status:</span>
                    <span className="text-sm font-medium">{formData.realEstateStatus || "Not provided"}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={cn("w-full max-w-6xl mx-auto", className)}>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              {isCreateMode && "Create New Listing"}
              {isEditMode && "Edit Listing"}
              {isViewMode && "Listing Details"}
            </h1>
            <p className="text-muted-foreground mt-2">
              {isCreateMode && "Add a new business listing to your portfolio"}
              {isEditMode && "Update the listing information"}
              {isViewMode && "View detailed information about this listing"}
            </p>
          </div>
          
          {!isViewMode && (
            <div className="flex items-center gap-3">
              <Button variant="outline" onClick={onCancel}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </div>
          )}
        </div>

        {/* Integrated Step Progress */}
        <StepProgress
          steps={steps.slice(0, isViewMode ? -1 : undefined)}
          currentStep={currentStep}
          completedSteps={completedSteps}
          onStepClick={handleStepClick}
          mode={mode}
        />
      </div>

      {/* Form content */}
      <Card>
        <CardContent className="space-y-6 pt-6">
          {renderStepContent()}
        </CardContent>
      </Card>

      {/* Navigation buttons */}
      {!isViewMode && (
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-4 mt-8">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={!canGoPrevious}
            className="order-2 sm:order-1"
            aria-label={`Go to previous step: ${canGoPrevious ? steps[currentStepIndex - 1]?.title : 'Not available'}`}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 order-1 sm:order-2">
            {/* Save/Save Draft button */}
            {currentStep !== "review" && (
              <Button
                variant={isEditMode ? "default" : "outline"}
                onClick={isEditMode ? handleEditSave : handleSaveDraft}
                disabled={isSubmitting}
                className={cn(
                  "transition-all duration-200 min-w-[120px]",
                  isEditMode && "bg-primary text-primary-foreground hover:bg-primary/90 shadow-md"
                )}
                aria-label={`${isEditMode ? 'Save current changes' : 'Save as draft'} for step: ${steps[currentStepIndex]?.title}`}
              >
                {isSubmitting ? (
                  <ButtonSpinner className="h-4 w-4 mr-2" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                {isEditMode ? "Save" : "Save Draft"}
              </Button>
            )}

            <Button
              onClick={currentStep === "review" ? handleSubmit : handleNext}
              disabled={!canGoNext && currentStep !== "review" || isSubmitting}
              className="min-w-[120px]"
              aria-label={
                currentStep === "review"
                  ? "Submit the complete listing"
                  : `Go to next step: ${canGoNext ? steps[currentStepIndex + 1]?.title : 'Not available'}`
              }
            >
              {isSubmitting ? (
                <ButtonSpinner className="h-4 w-4 mr-2" />
              ) : currentStep === "review" ? (
                "Submit Listing"
              ) : (
                <>
                  Next
                  <ArrowRight className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
