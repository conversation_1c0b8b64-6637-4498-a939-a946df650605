import React from 'react';
import { Setting<PERSON>, Shield, Users, Database, Bell, Palette } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useCurrentOrganizationQuery, useOrganizationMembersQuery } from '@/hooks/useOrganizationApi';
import { useAuth } from '@/contexts/AuthContext';

interface WorkspaceConfigurationProps {
  className?: string;
}

export const WorkspaceConfiguration: React.FC<WorkspaceConfigurationProps> = ({ className }) => {
  const { user } = useAuth();
  const { data: organization, isLoading: orgLoading } = useCurrentOrganizationQuery();
  const { data: members, isLoading: membersLoading } = useOrganizationMembersQuery();

  // Get current user's role
  const currentUserMember = members?.find(m => m.user.id === user?.id);
  const currentRole = currentUserMember?.role?.toLowerCase();
  const isOwnerOrAdmin = currentRole === 'owner' || currentRole === 'admin';

  // Calculate workspace statistics
  const workspaceStats = React.useMemo(() => {
    if (!members) return { totalMembers: 0, activeMembers: 0, roleDistribution: {} };

    const roleDistribution = members.reduce((acc, member) => {
      const role = member.role.toLowerCase();
      acc[role] = (acc[role] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalMembers: members.length,
      activeMembers: members.length, // Assuming all members are active for now
      roleDistribution,
    };
  }, [members]);

  const configurationSections = [
    {
      id: 'security',
      title: 'Security & Access',
      description: 'Manage workspace security settings and access controls',
      icon: Shield,
      settings: [
        {
          id: 'two_factor_required',
          label: 'Require Two-Factor Authentication',
          description: 'All members must enable 2FA to access the workspace',
          enabled: false,
          requiresOwner: true,
        },
        {
          id: 'session_timeout',
          label: 'Automatic Session Timeout',
          description: 'Automatically sign out inactive users after 8 hours',
          enabled: true,
          requiresOwner: false,
        },
        {
          id: 'ip_restrictions',
          label: 'IP Address Restrictions',
          description: 'Limit access to specific IP addresses or ranges',
          enabled: false,
          requiresOwner: true,
        },
      ],
    },
    {
      id: 'collaboration',
      title: 'Team Collaboration',
      description: 'Configure how team members work together',
      icon: Users,
      settings: [
        {
          id: 'public_profiles',
          label: 'Public Member Profiles',
          description: 'Allow team members to view each other\'s profiles',
          enabled: true,
          requiresOwner: false,
        },
        {
          id: 'cross_team_visibility',
          label: 'Cross-Team Visibility',
          description: 'Members can see listings from other team members',
          enabled: true,
          requiresOwner: false,
        },
        {
          id: 'comment_notifications',
          label: 'Comment Notifications',
          description: 'Notify members when they\'re mentioned in comments',
          enabled: true,
          requiresOwner: false,
        },
      ],
    },
    {
      id: 'data',
      title: 'Data & Storage',
      description: 'Manage data retention and storage policies',
      icon: Database,
      settings: [
        {
          id: 'auto_backup',
          label: 'Automatic Backups',
          description: 'Automatically backup workspace data daily',
          enabled: true,
          requiresOwner: true,
        },
        {
          id: 'data_retention',
          label: 'Data Retention Policy',
          description: 'Keep deleted items for 30 days before permanent deletion',
          enabled: true,
          requiresOwner: true,
        },
        {
          id: 'export_permissions',
          label: 'Data Export Permissions',
          description: 'Allow members to export their own data',
          enabled: true,
          requiresOwner: false,
        },
      ],
    },
    {
      id: 'notifications',
      title: 'Notifications',
      description: 'Configure workspace-wide notification settings',
      icon: Bell,
      settings: [
        {
          id: 'email_notifications',
          label: 'Email Notifications',
          description: 'Send email notifications for important events',
          enabled: true,
          requiresOwner: false,
        },
        {
          id: 'digest_emails',
          label: 'Weekly Digest Emails',
          description: 'Send weekly summary emails to all members',
          enabled: false,
          requiresOwner: false,
        },
        {
          id: 'system_announcements',
          label: 'System Announcements',
          description: 'Show system-wide announcements to all members',
          enabled: true,
          requiresOwner: true,
        },
      ],
    },
  ];

  if (orgLoading || membersLoading) {
    return (
      <div className={className}>
        <div className="space-y-6">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardHeader>
                <div className="animate-pulse">
                  <div className="h-5 bg-gray-200 rounded w-1/3 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[1, 2, 3].map((j) => (
                    <div key={j} className="animate-pulse">
                      <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Workspace Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Workspace Overview</span>
            </CardTitle>
            <CardDescription>
              Current workspace configuration and statistics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <div className="text-2xl font-bold">{workspaceStats.totalMembers}</div>
                <div className="text-sm text-muted-foreground">Total Members</div>
              </div>
              <div className="space-y-2">
                <div className="text-2xl font-bold">{workspaceStats.activeMembers}</div>
                <div className="text-sm text-muted-foreground">Active Members</div>
              </div>
              <div className="space-y-2">
                <div className="text-2xl font-bold">{organization?.name || 'N/A'}</div>
                <div className="text-sm text-muted-foreground">Organization</div>
              </div>
            </div>
            
            {Object.keys(workspaceStats.roleDistribution).length > 0 && (
              <>
                <Separator className="my-4" />
                <div>
                  <h4 className="text-sm font-medium mb-3">Role Distribution</h4>
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(workspaceStats.roleDistribution).map(([role, count]) => (
                      <Badge key={role} variant="outline" className="capitalize">
                        {role}: {count}
                      </Badge>
                    ))}
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Configuration Sections */}
        {configurationSections.map((section) => (
          <Card key={section.id}>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <section.icon className="h-5 w-5" />
                <span>{section.title}</span>
              </CardTitle>
              <CardDescription>{section.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {section.settings.map((setting, index) => (
                  <div key={setting.id}>
                    <div className="flex items-center justify-between">
                      <div className="space-y-1 flex-1">
                        <div className="flex items-center space-x-2">
                          <Label htmlFor={setting.id} className="text-sm font-medium">
                            {setting.label}
                          </Label>
                          {setting.requiresOwner && currentRole !== 'owner' && (
                            <Badge variant="secondary" className="text-xs">
                              Owner Only
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {setting.description}
                        </p>
                      </div>
                      <Switch
                        id={setting.id}
                        checked={setting.enabled}
                        disabled={setting.requiresOwner && currentRole !== 'owner'}
                        onCheckedChange={(checked) => {
                          // TODO: Implement setting update
                          console.log(`Setting ${setting.id} to ${checked}`);
                        }}
                      />
                    </div>
                    {index < section.settings.length - 1 && <Separator className="mt-6" />}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}

        {/* Permission Notice */}
        {!isOwnerOrAdmin && (
          <Card className="border-amber-200 bg-amber-50">
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 text-amber-800">
                <Shield className="h-4 w-4" />
                <span className="text-sm font-medium">
                  Limited Access
                </span>
              </div>
              <p className="text-sm text-amber-700 mt-2">
                Some configuration options require owner or admin permissions. 
                Contact your workspace owner to modify restricted settings.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};
