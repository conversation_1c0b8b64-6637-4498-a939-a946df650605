import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Building2, Upload, Save, AlertCircle, X } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import { Skeleton } from '@/components/ui/skeleton';
import { useCurrentOrganizationQuery, useUpdateOrganizationMutation } from '@/hooks/useOrganizationApi';
import { useUploadFileMutation } from '@/hooks/useQueryApi';
import type { components } from '@/types/api';

// Validation schema
const organizationSchema = z.object({
  name: z.string().min(1, 'Organization name is required').max(255, 'Name must be less than 255 characters'),
  logo: z.string().url('Invalid logo URL').optional().or(z.literal('')),
  metadata: z.record(z.any()).optional(),
});

type OrganizationFormData = z.infer<typeof organizationSchema>;

interface OrganizationSettingsProps {
  className?: string;
}

export const OrganizationSettings: React.FC<OrganizationSettingsProps> = ({ className }) => {
  const { data: organization, isLoading, error } = useCurrentOrganizationQuery();
  const updateOrganizationMutation = useUpdateOrganizationMutation();
  const uploadFileMutation = useUploadFileMutation();

  const [isUploadingLogo, setIsUploadingLogo] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  const form = useForm<OrganizationFormData>({
    resolver: zodResolver(organizationSchema),
    defaultValues: {
      name: '',
      logo: '',
      metadata: {},
    },
  });

  // Update form when organization data loads
  React.useEffect(() => {
    if (organization) {
      form.reset({
        name: organization.name || '',
        logo: organization.logo || '',
        metadata: organization.metadata ? JSON.parse(organization.metadata) : {},
      });
    }
  }, [organization, form]);

  const onSubmit = async (data: OrganizationFormData) => {
    try {
      await updateOrganizationMutation.mutateAsync({
        name: data.name,
        logo: data.logo || undefined,
        metadata: data.metadata,
      });
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setUploadError('Please select an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setUploadError('File size must be less than 5MB');
      return;
    }

    try {
      setIsUploadingLogo(true);
      setUploadError(null);

      const uploadResponse = await uploadFileMutation.mutateAsync({
        file,
        options: {
          fileType: 'image',
          entityType: 'organization',
          isPublic: true,
        },
      });

      // Update the form with the new logo URL
      const logoUrl = uploadResponse.file.signedUrl || '';
      form.setValue('logo', logoUrl);

      // Also update the organization immediately
      await updateOrganizationMutation.mutateAsync({
        logo: logoUrl,
      });

    } catch (error: any) {
      setUploadError(error?.message || 'Failed to upload logo. Please try again.');
    } finally {
      setIsUploadingLogo(false);
    }
  };

  const handleRemoveLogo = async () => {
    try {
      form.setValue('logo', '');
      await updateOrganizationMutation.mutateAsync({
        logo: undefined,
      });
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <Skeleton className="h-16 w-16 rounded-lg" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-8 w-24" />
              </div>
            </div>
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-32" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`border-destructive ${className}`}>
        <CardContent className="pt-6">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Unable to Load Organization</h3>
            <p className="text-muted-foreground">
              {error.message || 'An error occurred while loading organization data.'}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Building2 className="h-5 w-5" />
          <span>Organization Settings</span>
        </CardTitle>
        <CardDescription>
          Manage your organization details, logo, and basic information
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Logo Upload Section */}
            <div className="space-y-4">
              <Label>Organization Logo</Label>
              <div className="flex items-center space-x-4">
                <Avatar className="h-16 w-16 rounded-lg">
                  <AvatarImage 
                    src={form.watch('logo') || organization?.logo || undefined} 
                    alt={organization?.name || 'Organization logo'} 
                  />
                  <AvatarFallback className="rounded-lg">
                    <Building2 className="h-8 w-8" />
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      disabled={isUploadingLogo}
                      onClick={() => document.getElementById('logo-upload')?.click()}
                    >
                      {isUploadingLogo ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2" />
                          Uploading...
                        </>
                      ) : (
                        <>
                          <Upload className="h-4 w-4 mr-2" />
                          Upload Logo
                        </>
                      )}
                    </Button>
                    
                    {form.watch('logo') && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleRemoveLogo}
                        disabled={updateOrganizationMutation.isPending}
                      >
                        <X className="h-4 w-4 mr-2" />
                        Remove
                      </Button>
                    )}
                  </div>
                  
                  <input
                    id="logo-upload"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleLogoUpload}
                  />
                  
                  <p className="text-sm text-muted-foreground mt-1">
                    Recommended: Square image, max 5MB (PNG, JPG, SVG)
                  </p>
                  
                  {uploadError && (
                    <p className="text-sm text-destructive mt-1">{uploadError}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Organization Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Organization Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter organization name" {...field} />
                  </FormControl>
                  <FormDescription>
                    This name will be displayed throughout your workspace
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Organization Info Display */}
            {organization && (
              <div className="space-y-3 p-4 bg-muted/50 rounded-lg">
                <h4 className="font-medium text-sm">Organization Information</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">ID:</span>
                    <span className="ml-2 font-mono">{organization.id}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Created:</span>
                    <span className="ml-2">{new Date(organization.createdAt).toLocaleDateString()}</span>
                  </div>
                  {organization.slug && (
                    <div>
                      <span className="text-muted-foreground">Slug:</span>
                      <span className="ml-2 font-mono">{organization.slug}</span>
                    </div>
                  )}
                  {organization.companyType && (
                    <div>
                      <span className="text-muted-foreground">Type:</span>
                      <span className="ml-2 capitalize">{organization.companyType}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Save Button */}
            <div className="flex justify-end">
              <Button 
                type="submit" 
                disabled={updateOrganizationMutation.isPending || !form.formState.isDirty}
              >
                {updateOrganizationMutation.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
