import React, { useState } from 'react';
import { MoreHorizontal, Crown, Shield, UserCheck, Eye, UserX, Settings, Mail, Phone } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle 
} from '@/components/ui/alert-dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import { useOrganizationMembersQuery, useRemoveMemberMutation, useUpdateMemberRoleMutation } from '@/hooks/useOrganizationApi';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';
import type { components } from '@/types/api';

type Member = components['schemas']['Member'];

interface MembersListProps {
  className?: string;
  onInviteMember?: () => void;
  onManageRoles?: () => void;
}

export const MembersList: React.FC<MembersListProps> = ({ 
  className, 
  onInviteMember, 
  onManageRoles 
}) => {
  const { user } = useAuth();
  const { data: members, isLoading, error } = useOrganizationMembersQuery();
  const removeMemberMutation = useRemoveMemberMutation();
  const updateRoleMutation = useUpdateMemberRoleMutation();

  const [searchTerm, setSearchTerm] = useState('');
  const [memberToRemove, setMemberToRemove] = useState<Member | null>(null);
  const [memberToUpdateRole, setMemberToUpdateRole] = useState<{ member: Member; newRole: string } | null>(null);

  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case 'owner':
        return <Crown className="h-4 w-4" />;
      case 'admin':
        return <Shield className="h-4 w-4" />;
      case 'member':
        return <UserCheck className="h-4 w-4" />;
      case 'viewer':
        return <Eye className="h-4 w-4" />;
      default:
        return <UserCheck className="h-4 w-4" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'owner':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'admin':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'member':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'viewer':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getInitials = (firstName?: string | null, lastName?: string | null, name?: string | null) => {
    if (firstName && lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    }
    if (name) {
      const parts = name.split(' ');
      if (parts.length >= 2) {
        return `${parts[0].charAt(0)}${parts[1].charAt(0)}`.toUpperCase();
      }
      return name.charAt(0).toUpperCase();
    }
    return 'U';
  };

  const getDisplayName = (member: Member) => {
    if (member.profile?.displayName) return member.profile.displayName;
    if (member.profile?.firstName && member.profile?.lastName) {
      return `${member.profile.firstName} ${member.profile.lastName}`;
    }
    if (member.user.name) return member.user.name;
    return member.user.email;
  };

  const isCurrentUser = (member: Member) => {
    return member.user.id === user?.id;
  };

  const canManageMember = (member: Member) => {
    // Can't manage yourself
    if (isCurrentUser(member)) return false;
    
    // Only owners and admins can manage members
    // Owners can manage everyone, admins can manage members and viewers
    const currentUserMember = members?.find(m => m.user.id === user?.id);
    if (!currentUserMember) return false;

    const currentRole = currentUserMember.role.toLowerCase();
    const targetRole = member.role.toLowerCase();

    if (currentRole === 'owner') return true;
    if (currentRole === 'admin' && !['owner', 'admin'].includes(targetRole)) return true;
    
    return false;
  };

  const handleRemoveMember = async () => {
    if (!memberToRemove) return;
    
    try {
      await removeMemberMutation.mutateAsync(memberToRemove.user.email);
      setMemberToRemove(null);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleUpdateRole = async (member: Member, newRole: string) => {
    try {
      await updateRoleMutation.mutateAsync({
        memberId: member.id,
        roleData: { role: newRole as 'admin' | 'member' }
      });
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  // Filter members based on search term
  const filteredMembers = React.useMemo(() => {
    if (!members) return [];
    if (!searchTerm) return members;

    return members.filter(member => {
      const displayName = getDisplayName(member).toLowerCase();
      const email = member.user.email.toLowerCase();
      const role = member.role.toLowerCase();
      const search = searchTerm.toLowerCase();

      return displayName.includes(search) || 
             email.includes(search) || 
             role.includes(search);
    });
  }, [members, searchTerm]);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Team Members</CardTitle>
          <CardDescription>Loading team members...</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-48" />
              </div>
              <Skeleton className="h-6 w-16" />
              <Skeleton className="h-8 w-8" />
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("border-destructive", className)}>
        <CardContent className="pt-6">
          <div className="text-center">
            <UserX className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Unable to Load Members</h3>
            <p className="text-muted-foreground">
              {error.message || 'An error occurred while loading team members.'}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Team Members ({filteredMembers.length})</CardTitle>
              <CardDescription>
                Manage your team members and their roles
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              {onManageRoles && (
                <Button variant="outline" size="sm" onClick={onManageRoles}>
                  <Settings className="h-4 w-4 mr-2" />
                  Manage Roles
                </Button>
              )}
              {onInviteMember && (
                <Button size="sm" onClick={onInviteMember}>
                  <Mail className="h-4 w-4 mr-2" />
                  Invite Member
                </Button>
              )}
            </div>
          </div>
          
          {/* Search */}
          <div className="pt-4">
            <Input
              placeholder="Search members by name, email, or role..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {filteredMembers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {searchTerm ? 'No members found matching your search.' : 'No team members found.'}
            </div>
          ) : (
            filteredMembers.map((member) => (
              <div key={member.id} className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={member.user.image || undefined} />
                  <AvatarFallback>
                    {getInitials(
                      member.profile?.firstName,
                      member.profile?.lastName,
                      member.user.name
                    )}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h3 className="font-semibold text-sm truncate">
                      {getDisplayName(member)}
                    </h3>
                    {isCurrentUser(member) && (
                      <Badge variant="secondary" className="text-xs">You</Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground truncate">
                    {member.user.email}
                  </p>
                  <div className="text-xs text-muted-foreground">
                    Joined {new Date(member.createdAt).toLocaleDateString()}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Badge className={cn("flex items-center space-x-1", getRoleColor(member.role))}>
                    {getRoleIcon(member.role)}
                    <span className="capitalize">{member.role}</span>
                  </Badge>
                  
                  {canManageMember(member) && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleUpdateRole(member, 'admin')}>
                          <Shield className="h-4 w-4 mr-2" />
                          Make Admin
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleUpdateRole(member, 'member')}>
                          <UserCheck className="h-4 w-4 mr-2" />
                          Make Member
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => setMemberToRemove(member)}
                          className="text-destructive"
                        >
                          <UserX className="h-4 w-4 mr-2" />
                          Remove Member
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>

      {/* Remove Member Confirmation Dialog */}
      <AlertDialog open={!!memberToRemove} onOpenChange={() => setMemberToRemove(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Team Member</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove {memberToRemove && getDisplayName(memberToRemove)} from the team? 
              This action cannot be undone and they will lose access to all workspace resources.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleRemoveMember}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={removeMemberMutation.isPending}
            >
              {removeMemberMutation.isPending ? 'Removing...' : 'Remove Member'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
