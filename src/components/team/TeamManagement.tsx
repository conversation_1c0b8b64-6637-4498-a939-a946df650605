import React, { useState } from 'react';
import { Users, UserPlus, Settings, BarChart3 } from 'lucide-react';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { TeamOverview } from './TeamOverview';
import { MembersList } from './MembersList';
import { InviteMemberForm } from './InviteMemberForm';
import { MemberRoleManager } from './MemberRoleManager';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganizationMembersQuery } from '@/hooks/useOrganizationApi';

interface TeamManagementProps {
  className?: string;
}

const TeamManagement: React.FC<TeamManagementProps> = ({ className }) => {
  const { user } = useAuth();
  const { data: members } = useOrganizationMembersQuery();

  const [activeTab, setActiveTab] = useState('overview');
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [isRoleManagerOpen, setIsRoleManagerOpen] = useState(false);

  // Permission checks
  const currentUserMember = members?.find(m => m.user.id === user?.id);
  const currentRole = currentUserMember?.role?.toLowerCase();
  const canManageTeam = currentRole === 'owner' || currentRole === 'admin';
  const canInvite = canManageTeam;
  const canManageRoles = currentRole === 'owner' || currentRole === 'admin';

  const handleInviteMember = () => {
    setIsInviteModalOpen(true);
  };

  const handleManageRoles = () => {
    setIsRoleManagerOpen(true);
  };

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Team Management</h1>
            <p className="text-muted-foreground">
              Manage your workspace team members, roles, and permissions
            </p>
          </div>
          <div className="flex space-x-2">
            {canManageRoles && (
              <Button variant="outline" onClick={handleManageRoles}>
                <Settings className="h-4 w-4 mr-2" />
                Manage Roles
              </Button>
            )}
            {canInvite && (
              <Button onClick={handleInviteMember}>
                <UserPlus className="h-4 w-4 mr-2" />
                Invite Member
              </Button>
            )}
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="overview" className="flex items-center space-x-2">
              <BarChart3 className="h-4 w-4" />
              <span>Overview</span>
            </TabsTrigger>
            <TabsTrigger value="members" className="flex items-center space-x-2">
              <Users className="h-4 w-4" />
              <span>Members</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <TeamOverview />
          </TabsContent>

          <TabsContent value="members" className="space-y-6">
            <MembersList
              onInviteMember={canInvite ? handleInviteMember : undefined}
              onManageRoles={canManageRoles ? handleManageRoles : undefined}
            />
          </TabsContent>
        </Tabs>

        {/* Modals */}
        <InviteMemberForm
          open={isInviteModalOpen}
          onOpenChange={setIsInviteModalOpen}
        />

        <MemberRoleManager
          open={isRoleManagerOpen}
          onOpenChange={setIsRoleManagerOpen}
        />
      </div>
    </div>
  );
};

export default TeamManagement;