import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Mail, UserPlus, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useInviteMemberMutation } from '@/hooks/useOrganizationApi';
import type { components } from '@/types/api';

// Validation schema
const inviteMemberSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  role: z.enum(['admin', 'member'], {
    required_error: 'Please select a role',
  }),
  resend: z.boolean().default(false),
});

type InviteMemberFormData = z.infer<typeof inviteMemberSchema>;

interface InviteMemberFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  className?: string;
}

export const InviteMemberForm: React.FC<InviteMemberFormProps> = ({
  open,
  onOpenChange,
  className
}) => {
  const inviteMemberMutation = useInviteMemberMutation();
  const [invitedEmails, setInvitedEmails] = useState<string[]>([]);

  const form = useForm<InviteMemberFormData>({
    resolver: zodResolver(inviteMemberSchema),
    defaultValues: {
      email: '',
      role: 'member',
      resend: false,
    },
  });

  const onSubmit = async (data: InviteMemberFormData) => {
    try {
      await inviteMemberMutation.mutateAsync({
        email: data.email,
        role: data.role,
        resend: data.resend,
      });

      // Add to invited emails list for this session
      setInvitedEmails(prev => [...prev, data.email]);
      
      // Reset form
      form.reset();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleClose = () => {
    form.reset();
    setInvitedEmails([]);
    onOpenChange(false);
  };

  const removeInvitedEmail = (email: string) => {
    setInvitedEmails(prev => prev.filter(e => e !== email));
  };

  const getRoleDescription = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Can manage team members, settings, and all workspace features';
      case 'member':
        return 'Can access and manage listings and basic workspace features';
      default:
        return '';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <UserPlus className="h-5 w-5" />
            <span>Invite Team Member</span>
          </DialogTitle>
          <DialogDescription>
            Send an invitation to join your workspace as a team member.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Recently Invited Members */}
          {invitedEmails.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Recently Invited</CardTitle>
                <CardDescription className="text-xs">
                  Invitations sent in this session
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                {invitedEmails.map((email) => (
                  <div key={email} className="flex items-center justify-between p-2 bg-muted rounded-md">
                    <div className="flex items-center space-x-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{email}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={() => removeInvitedEmail(email)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Invitation Form */}
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Enter the email address of the person you want to invite
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="member">
                          <div className="flex items-center space-x-2">
                            <span>Member</span>
                            <Badge variant="outline" className="text-xs">Default</Badge>
                          </div>
                        </SelectItem>
                        <SelectItem value="admin">
                          <div className="flex items-center space-x-2">
                            <span>Admin</span>
                            <Badge variant="secondary" className="text-xs">Advanced</Badge>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      {getRoleDescription(form.watch('role'))}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Role Permissions Info */}
              <Card className="bg-muted/50">
                <CardContent className="pt-4">
                  <div className="text-sm space-y-2">
                    <div className="font-medium">Role Permissions:</div>
                    {form.watch('role') === 'admin' ? (
                      <ul className="text-xs text-muted-foreground space-y-1">
                        <li>• Invite and manage team members</li>
                        <li>• Access workspace settings</li>
                        <li>• Manage all listings and data</li>
                        <li>• View team analytics and reports</li>
                      </ul>
                    ) : (
                      <ul className="text-xs text-muted-foreground space-y-1">
                        <li>• Create and manage listings</li>
                        <li>• View team listings</li>
                        <li>• Access basic reports</li>
                        <li>• Update own profile</li>
                      </ul>
                    )}
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  disabled={inviteMemberMutation.isPending}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={inviteMemberMutation.isPending}
                >
                  {inviteMemberMutation.isPending ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Mail className="h-4 w-4 mr-2" />
                      Send Invitation
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>

          {/* Help Text */}
          <div className="text-xs text-muted-foreground bg-muted/30 p-3 rounded-md">
            <strong>Note:</strong> The invited person will receive an email with instructions to join your workspace. 
            They'll need to create an account or sign in if they already have one.
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
