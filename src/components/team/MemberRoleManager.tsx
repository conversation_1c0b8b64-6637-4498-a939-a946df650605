import React, { useState } from 'react';
import { <PERSON>ting<PERSON>, Crown, Shield, User<PERSON>he<PERSON>, Eye, Save, X } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { useOrganizationMembersQuery, useUpdateMemberRoleMutation } from '@/hooks/useOrganizationApi';
import { useAuth } from '@/contexts/AuthContext';
import { cn } from '@/lib/utils';
import type { components } from '@/types/api';

type Member = components['schemas']['Member'];

interface MemberRoleManagerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  className?: string;
}

interface RoleChange {
  memberId: string;
  currentRole: string;
  newRole: string;
}

export const MemberRoleManager: React.FC<MemberRoleManagerProps> = ({
  open,
  onOpenChange,
  className
}) => {
  const { user } = useAuth();
  const { data: members, isLoading, error } = useOrganizationMembersQuery();
  const updateRoleMutation = useUpdateMemberRoleMutation();

  const [roleChanges, setRoleChanges] = useState<Record<string, string>>({});
  const [isUpdating, setIsUpdating] = useState(false);

  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case 'owner':
        return <Crown className="h-4 w-4" />;
      case 'admin':
        return <Shield className="h-4 w-4" />;
      case 'member':
        return <UserCheck className="h-4 w-4" />;
      case 'viewer':
        return <Eye className="h-4 w-4" />;
      default:
        return <UserCheck className="h-4 w-4" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'owner':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'admin':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'member':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'viewer':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getInitials = (firstName?: string | null, lastName?: string | null, name?: string | null) => {
    if (firstName && lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    }
    if (name) {
      const parts = name.split(' ');
      if (parts.length >= 2) {
        return `${parts[0].charAt(0)}${parts[1].charAt(0)}`.toUpperCase();
      }
      return name.charAt(0).toUpperCase();
    }
    return 'U';
  };

  const getDisplayName = (member: Member) => {
    if (member.profile?.displayName) return member.profile.displayName;
    if (member.profile?.firstName && member.profile?.lastName) {
      return `${member.profile.firstName} ${member.profile.lastName}`;
    }
    if (member.user.name) return member.user.name;
    return member.user.email;
  };

  const isCurrentUser = (member: Member) => {
    return member.user.id === user?.id;
  };

  const canChangeRole = (member: Member) => {
    // Can't change your own role
    if (isCurrentUser(member)) return false;
    
    // Can't change owner role
    if (member.role.toLowerCase() === 'owner') return false;
    
    // Only owners and admins can change roles
    const currentUserMember = members?.find(m => m.user.id === user?.id);
    if (!currentUserMember) return false;

    const currentRole = currentUserMember.role.toLowerCase();
    return currentRole === 'owner' || currentRole === 'admin';
  };

  const handleRoleChange = (memberId: string, newRole: string) => {
    setRoleChanges(prev => ({
      ...prev,
      [memberId]: newRole
    }));
  };

  const getEffectiveRole = (member: Member) => {
    return roleChanges[member.id] || member.role;
  };

  const hasChanges = Object.keys(roleChanges).length > 0;

  const handleSaveChanges = async () => {
    if (!hasChanges) return;

    setIsUpdating(true);
    try {
      // Apply all role changes
      for (const [memberId, newRole] of Object.entries(roleChanges)) {
        await updateRoleMutation.mutateAsync({
          memberId,
          roleData: { role: newRole as 'admin' | 'member' }
        });
      }
      
      // Clear changes and close dialog
      setRoleChanges({});
      onOpenChange(false);
    } catch (error) {
      // Error is handled by the mutation
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancel = () => {
    setRoleChanges({});
    onOpenChange(false);
  };

  const availableRoles = [
    { value: 'admin', label: 'Admin', description: 'Can manage team and settings' },
    { value: 'member', label: 'Member', description: 'Can access and manage listings' },
  ];

  if (isLoading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>Manage Member Roles</DialogTitle>
            <DialogDescription>Loading team members...</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-48" />
                </div>
                <Skeleton className="h-8 w-24" />
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Manage Member Roles</span>
          </DialogTitle>
          <DialogDescription>
            Update team member roles and permissions. Changes will be applied when you save.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {error ? (
            <Card className="border-destructive">
              <CardContent className="pt-6">
                <div className="text-center">
                  <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Unable to Load Members</h3>
                  <p className="text-muted-foreground">
                    {error.message || 'An error occurred while loading team members.'}
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <>
              {/* Role Changes Summary */}
              {hasChanges && (
                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="pt-4">
                    <div className="text-sm">
                      <div className="font-medium text-blue-900 mb-2">
                        Pending Changes ({Object.keys(roleChanges).length})
                      </div>
                      <div className="text-blue-700">
                        Click "Save Changes" to apply all role updates.
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Members List */}
              <div className="space-y-3">
                {members?.map((member) => (
                  <div key={member.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={member.user.image || undefined} />
                      <AvatarFallback>
                        {getInitials(
                          member.profile?.firstName,
                          member.profile?.lastName,
                          member.user.name
                        )}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold text-sm truncate">
                          {getDisplayName(member)}
                        </h3>
                        {isCurrentUser(member) && (
                          <Badge variant="secondary" className="text-xs">You</Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground truncate">
                        {member.user.email}
                      </p>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      {/* Current Role Badge */}
                      <Badge className={cn("flex items-center space-x-1", getRoleColor(getEffectiveRole(member)))}>
                        {getRoleIcon(getEffectiveRole(member))}
                        <span className="capitalize">{getEffectiveRole(member)}</span>
                      </Badge>
                      
                      {/* Role Selector */}
                      {canChangeRole(member) ? (
                        <Select
                          value={getEffectiveRole(member)}
                          onValueChange={(value) => handleRoleChange(member.id, value)}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {availableRoles.map((role) => (
                              <SelectItem key={role.value} value={role.value}>
                                <div className="flex items-center space-x-2">
                                  {getRoleIcon(role.value)}
                                  <span>{role.label}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      ) : (
                        <div className="w-32 text-center">
                          <span className="text-xs text-muted-foreground">
                            {member.role.toLowerCase() === 'owner' ? 'Owner' : 'No access'}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isUpdating}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            onClick={handleSaveChanges}
            disabled={!hasChanges || isUpdating}
          >
            {isUpdating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
